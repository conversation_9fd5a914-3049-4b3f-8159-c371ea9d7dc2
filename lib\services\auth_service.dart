import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Stream para observar mudanças no estado de autenticação
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Usuário atual
  User? get currentUser => _auth.currentUser;

  // Verificar se o usuário está logado
  bool get isLoggedIn => currentUser != null;

  // Cadastrar usuário com email e senha
  Future<UserCredential> signUpWithEmailAndPassword({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      // Criar usuário no Firebase Auth
      final UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);

      // Atualizar o displayName do usuário
      await userCredential.user?.updateDisplayName(name);

      // Salvar dados adicionais do usuário no Firestore
      if (userCredential.user != null) {
        final appUser = AppUser.create(name: name, email: email);

        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .set(appUser.toMap());
      }

      return userCredential;
    } catch (e) {
      throw AuthException('Erro ao cadastrar usuário: $e');
    }
  }

  // Login com email e senha
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw AuthException('Erro ao fazer login: $e');
    }
  }

  // Login com Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      // Cria uma instância do provedor de autenticação do Google.
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // Você pode adicionar escopos para solicitar permissões adicionais.
      // Exemplo: para ler os contatos do usuário.
      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.setCustomParameters({'login_hint': '<EMAIL>'});

      // Faz o login com um Pop-up. O Firebase gerencia o fluxo para você.
      final UserCredential userCredential = await _auth.signInWithPopup(
        googleProvider,
      );

      return userCredential;
    } on FirebaseAuthException catch (e) {
      // Trata erros específicos do Firebase Auth
      throw AuthException('Erro ao fazer login com Google: ${e.message}');
    } catch (error) {
      // Trata outros possíveis erros
      throw AuthException('Erro ao fazer login com Google: $error');
    }
  }

  // Logout
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Obter informações do usuário
  AuthUser? getUserInfo() {
    final user = currentUser;
    if (user == null) return null;

    return AuthUser(
      uid: user.uid,
      email: user.email ?? '',
      displayName: user.displayName ?? '',
      photoURL: user.photoURL,
    );
  }

  // Obter dados completos do usuário do Firestore
  Future<AppUser?> getUserData() async {
    try {
      final user = currentUser;
      if (user == null) return null;

      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        return AppUser.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw AuthException('Erro ao obter dados do usuário: $e');
    }
  }

  // Deletar conta
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Deletar dados do Firestore
        await _firestore.collection('users').doc(user.uid).delete();

        // Deletar conta do Firebase Auth
        await user.delete();
      }
    } catch (e) {
      throw AuthException('Erro ao deletar conta: $e');
    }
  }

  // Resetar senha
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw AuthException('Erro ao enviar email de recuperação: $e');
    }
  }
}

// Classe para representar dados do usuário
class AuthUser {
  final String uid;
  final String email;
  final String displayName;
  final String? photoURL;

  AuthUser({
    required this.uid,
    required this.email,
    required this.displayName,
    this.photoURL,
  });

  // Obter iniciais do nome
  String get initials {
    if (displayName.isEmpty) return '?';

    List<String> words = displayName.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[words.length - 1][0]}'.toUpperCase();
    }
  }

  @override
  String toString() {
    return 'AuthUser{uid: $uid, email: $email, displayName: $displayName, photoURL: $photoURL}';
  }
}

// Exceção personalizada para autenticação
class AuthException implements Exception {
  final String message;

  AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
